import React from 'react';
import { useAppStore } from '../../stores/app-store';

interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * ThemeProvider component that applies the current theme to the DOM
 * This ensures that CSS variables and theme-specific styles are applied correctly
 */
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { theme } = useAppStore();

  React.useEffect(() => {
    // Apply data-theme attribute to document element for CSS variables
    document.documentElement.setAttribute('data-theme', theme);

    // Also apply class for Tailwind CSS dark mode
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
      document.documentElement.classList.remove('light');
    } else {
      document.documentElement.classList.add('light');
      document.documentElement.classList.remove('dark');
    }

    console.log('🎨 Theme applied:', theme);
  }, [theme]);

  return <>{children}</>;
};
